{"name": "test-github-action-frontend", "version": "1.0.0", "description": "Frontend application for test GitHub Action", "main": "src/Main.js", "scripts": {"dev": "vite", "build": "vite build", "dev-build": "vite build", "preview": "vite preview", "lint": "eslint ."}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@stylistic/eslint-plugin": "^2.10.1", "@vitejs/plugin-react": "^4.3.3", "eslint": "^8.57.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "vite": "^5.4.10"}}