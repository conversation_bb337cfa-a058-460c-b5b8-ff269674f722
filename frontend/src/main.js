import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './styles.css'; // Assuming you have a styles file

// Entry point for the React application
function initializeApp() {
  const rootElement = document.getElementById('root');
  
  if (!rootElement) {
    console.error('Failed to find the root element');
    return;
  }
  
  const root = ReactDOM.createRoot(rootElement);
  
  root.render(
    <React.StrictMode>
      <App />
    </React.StrictMode>
  );
}

// Initialize the app when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', initializeApp);

// For hot module replacement if using a bundler like Webpack
if (module.hot) {
  module.hot.accept();
}