import React, { useState, useEffect } from 'react';

function App() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [newUser, setNewUser] = useState({ name: '', email: '' });

  const API_URL = 'http://localhost:3000/api';

  // Fetch users from the API
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API_URL}/users`);
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      const data = await response.json();
      setUsers(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch users');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Add a new user
  const addUser = async (e) => {
    e.preventDefault();
    
    if (!newUser.name || !newUser.email) {
      setError('Name and email are required');
      return;
    }
    
    setLoading(true);
    try {
      const response = await fetch(`${API_URL}/users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newUser)
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      const addedUser = await response.json();
      setUsers([...users, addedUser]);
      setNewUser({ name: '', email: '' });
      setError(null);
    } catch (err) {
      setError('Failed to add user');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setNewUser({
      ...newUser,
      [name]: value
    });
  };

  // Load users when component mounts
  useEffect(() => {
    fetchUsers();
  }, []);

  return (
    <div className="app-container">
      <h1>User Management</h1>
      
      {error && <div className="error-message">{error}</div>}
      
      <div className="user-form">
        <h2>Add New User</h2>
        <form onSubmit={addUser}>
          <div className="form-group">
            <label htmlFor="name">Name:</label>
            <input
              type="text"
              id="name"
              name="name"
              value={newUser.name}
              onChange={handleChange}
              placeholder="Enter name"
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="email">Email:</label>
            <input
              type="email"
              id="email"
              name="email"
              value={newUser.email}
              onChange={handleChange}
              placeholder="Enter email"
            />
          </div>
          
          <button type="submit" disabled={loading}>
            {loading ? 'Adding...' : 'Add User'}
          </button>
        </form>
      </div>
      
      <div className="user-list">
        <h2>Users</h2>
        {loading && <p>Loading...</p>}
        {!loading && users.length === 0 && <p>No users found</p>}
        {!loading && users.length > 0 && (
          <ul>
            {users.map(user => (
              <li key={user.id}>
                <strong>{user.name}</strong> ({user.email})
              </li>
            ))}
          </ul>
        )}
        <button onClick={fetchUsers} disabled={loading}>
          Refresh Users
        </button>
      </div>
    </div>
  );
}

export default App;