{"name": "node-app", "version": "1.0.0", "description": "Ứng dụng Node.js c<PERSON> bản", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint ."}, "keywords": [], "author": "danghh-vnlab <<EMAIL>>", "license": "ISC", "dependencies": {"express": "^4.18.2"}, "devDependencies": {"@stylistic/eslint-plugin": "^1.5.3", "eslint": "^9.0.0", "eslint-plugin-import": "^2.31.0", "globals": "^13.24.0"}, "repository": "https://github.com/danghh-vnlab/test-github-action.git"}