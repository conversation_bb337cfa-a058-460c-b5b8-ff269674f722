name: Lint

on:
  pull_request:
    branches: [ develop ]
    paths:
      - '**/*.js'
      - '**/*.jsx'
      - '**/*.css'
      - '**/*.scss'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lint-backend:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    defaults:
      run:
        working-directory: ./backend
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'
          cache-dependency-path: './backend/yarn.lock'
      - name: Install dependencies
        run: yarn install --prefer-offline
      - name: Run ESLint for backend
        run: yarn lint || echo "ESLint found issues but continuing..."
        continue-on-error: true

  lint-frontend:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    defaults:
      run:
        working-directory: ./frontend
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'
          cache-dependency-path: './frontend/yarn.lock'
      - name: Install dependencies
        run: yarn install --prefer-offline
      - name: Run ESLint for frontend
        run: yarn lint || echo "ESLint found issues but continuing..."
        continue-on-error: true
