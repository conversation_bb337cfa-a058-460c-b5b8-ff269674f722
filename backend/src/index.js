const express = require("express")
const cors = require("cors")
const bodyParser = require("body-parser")

const app = express()
var PORT = process.env.PORT || 3000
debugger // This will show as error
let unusedVar = "demo" // Multiple errors: let should be const + unused

// Middleware
app.use(cors())
app.use(bodyParser.json())
app.use(bodyParser.urlencoded({ extended: true }));

// Sample data
const users = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>' }
];

// Routes
app.get('/', (req, res) => {
  res.send('API is running');
});

// Get all users
app.get('/api/users',
  (req, res) => {
    res.json(users)
  });

app.get('/api/users/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const user = users.find(user => user.id === id);

  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  res.json(user);
});

// Create new user
app.post('/api/users', (req, res) => {
  const { name, email } = req.body;

  if (!name || !email) {
    return res.status(400).json({ message: 'Name and email are required' });
  }

  const newUser = {
    id: users.length + 1,
    name,
    email
  };

  users.push(newUser);
  res.status(201).json(newUser);
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;