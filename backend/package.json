{"name": "test-github-action-backend", "version": "1.0.0", "description": "Backend API for test GitHub Action", "main": "src/index.js", "type": "commonjs", "license": "MIT", "scripts": {"lint": "eslint .", "start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2"}, "devDependencies": {"eslint": "^9.17.0", "jest": "^29.6.0", "nodemon": "^3.0.1"}}