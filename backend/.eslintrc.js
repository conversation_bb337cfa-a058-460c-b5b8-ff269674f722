module.exports = {
  env: {
    node: true,
    es2021: true,
  },
  extends: [
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 12,
    sourceType: 'module',
  },
  rules: {
    // Code quality warnings (show but don't fail build)
    'no-unused-vars': ['warn', {
      vars: 'all',
      args: 'after-used',
      ignoreRestSiblings: true,
    }],
    'no-console': 'off', // Allow console in backend
    'no-debugger': 'warn',
    'no-var': 'warn',
    'prefer-const': 'warn',
    'prefer-arrow-callback': 'warn',

    // Style warnings
    'semi': ['warn', 'always'],
    'quotes': ['warn', 'single', { avoidEscape: true }],
    'indent': ['warn', 2],
    'comma-dangle': ['warn', 'always-multiline'],
    'object-curly-spacing': ['warn', 'always'],
    'array-bracket-spacing': ['warn', 'never'],
    'space-before-blocks': 'warn',
    'keyword-spacing': 'warn',
    'space-infix-ops': 'warn',
    'eol-last': 'warn',

    // Node.js specific warnings
    'no-process-exit': 'warn',
    'no-path-concat': 'warn',
    'handle-callback-err': 'warn',

    // Import/Export warnings
    'no-duplicate-imports': 'warn',
    'no-useless-rename': 'warn',

    // Security warnings
    'no-eval': 'warn',
    'no-implied-eval': 'warn',
    'no-new-func': 'warn',
  },
};
