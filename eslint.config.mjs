import stylistic from '@stylistic/eslint-plugin';
import importPlugin from 'eslint-plugin-import';
import globals from 'globals';

export default [
  {
    files: ['**/*.js', '**/*.mjs'],
    languageOptions: {
      sourceType: 'module',
      ecmaVersion: 2022,
      globals: {
        ...globals.node
      }
    },
    plugins: {
      stylistic,
      import: importPlugin
    },
    rules: {
      'stylistic/semi': 'error',
      'stylistic/indent': ['error', 2],
      'stylistic/quotes': ['error', 'single'],
      'stylistic/comma-dangle': ['error', 'never'],
      'no-unused-vars': 'warn',
      'no-console': 'off',
      'no-undef': 'error',
      'import/order': [
        'error',
        { alphabetize: { order: 'asc' } }
      ]
    }
  }
];
